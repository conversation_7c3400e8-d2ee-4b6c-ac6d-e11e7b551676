"""
Modelos de dados SQLAlchemy para o GitHub Portfolio Analyzer
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey, Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import os
from dotenv import load_dotenv

load_dotenv()

Base = declarative_base()

# Tabela de associação para relacionamento N-M entre repos e linguagens
repo_languages = Table(
    'repo_languages',
    Base.metadata,
    Column('repo_id', Integer, ForeignKey('repositories.id'), primary_key=True),
    Column('language_id', Integer, ForeignKey('languages.id'), primary_key=True),
    Column('bytes_count', Integer, default=0),
    Column('percentage', Float, default=0.0)
)

class User(Base):
    """Modelo para usuários do GitHub"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(100), unique=True, nullable=False)
    name = Column(String(200))
    bio = Column(Text)
    location = Column(String(100))
    company = Column(String(100))
    blog = Column(String(200))
    email = Column(String(100))
    avatar_url = Column(String(500))
    followers = Column(Integer, default=0)
    following = Column(Integer, default=0)
    public_repos = Column(Integer, default=0)
    public_gists = Column(Integer, default=0)
    created_at = Column(DateTime)
    updated_at = Column(DateTime, default=datetime.utcnow)
    last_fetched = Column(DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    repositories = relationship("Repository", back_populates="owner")
    
    def __repr__(self):
        return f"<User(username='{self.username}', name='{self.name}')>"

class Repository(Base):
    """Modelo para repositórios do GitHub"""
    __tablename__ = 'repositories'
    
    id = Column(Integer, primary_key=True)
    github_id = Column(Integer, unique=True, nullable=False)
    name = Column(String(200), nullable=False)
    full_name = Column(String(300), nullable=False)
    description = Column(Text)
    private = Column(Boolean, default=False)
    fork = Column(Boolean, default=False)
    archived = Column(Boolean, default=False)
    disabled = Column(Boolean, default=False)
    
    # URLs
    html_url = Column(String(500))
    clone_url = Column(String(500))
    git_url = Column(String(500))
    ssh_url = Column(String(500))
    
    # Estatísticas
    size = Column(Integer, default=0)
    stargazers_count = Column(Integer, default=0)
    watchers_count = Column(Integer, default=0)
    forks_count = Column(Integer, default=0)
    open_issues_count = Column(Integer, default=0)
    
    # Linguagem principal
    primary_language = Column(String(50))
    
    # Datas
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    pushed_at = Column(DateTime)
    last_fetched = Column(DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    owner_id = Column(Integer, ForeignKey('users.id'))
    owner = relationship("User", back_populates="repositories")
    languages = relationship("Language", secondary=repo_languages, back_populates="repositories")
    commits = relationship("Commit", back_populates="repository")
    
    def __repr__(self):
        return f"<Repository(name='{self.name}', stars={self.stargazers_count})>"

class Language(Base):
    """Modelo para linguagens de programação"""
    __tablename__ = 'languages'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    color = Column(String(7))  # Cor hexadecimal
    
    # Relacionamentos
    repositories = relationship("Repository", secondary=repo_languages, back_populates="languages")
    
    def __repr__(self):
        return f"<Language(name='{self.name}')>"

class Commit(Base):
    """Modelo para commits"""
    __tablename__ = 'commits'
    
    id = Column(Integer, primary_key=True)
    sha = Column(String(40), unique=True, nullable=False)
    message = Column(Text)
    author_name = Column(String(100))
    author_email = Column(String(100))
    committer_name = Column(String(100))
    committer_email = Column(String(100))
    date = Column(DateTime)
    additions = Column(Integer, default=0)
    deletions = Column(Integer, default=0)
    
    # Relacionamentos
    repository_id = Column(Integer, ForeignKey('repositories.id'))
    repository = relationship("Repository", back_populates="commits")
    
    def __repr__(self):
        return f"<Commit(sha='{self.sha[:8]}', date='{self.date}')>"

class DatabaseManager:
    """Gerenciador de banco de dados"""
    
    def __init__(self, database_url=None):
        if database_url is None:
            database_url = os.getenv('DATABASE_URL', 'sqlite:///repos.db')
        
        self.engine = create_engine(database_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
    def create_tables(self):
        """Cria todas as tabelas no banco de dados"""
        Base.metadata.create_all(bind=self.engine)
        
    def get_session(self):
        """Retorna uma nova sessão do banco de dados"""
        return self.SessionLocal()
        
    def drop_tables(self):
        """Remove todas as tabelas (cuidado!)"""
        Base.metadata.drop_all(bind=self.engine)

# Instância global do gerenciador
db_manager = DatabaseManager()
