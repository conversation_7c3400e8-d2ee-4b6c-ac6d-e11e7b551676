# 📊 GitHub Portfolio Analyzer

Um dashboard interativo e moderno para analisar portfólios do GitHub com Streamlit.

## ✨ Funcionalidades

- **Dashboard Multi-abas**: Visão Geral, Linguagens, Atividade e Detalhes
- **Análises <PERSON>n<PERSON>**: Heatmap de commits, word cloud, análise de linguagens
- **Comparação de Usuários**: Compare até 3 perfis GitHub lado a lado
- **Cache Inteligente**: Performance otimizada com cache de dados
- **Exportação**: Relatórios em CSV e PDF
- **Interface Moderna**: UI responsiva com tema claro/escuro

## 🚀 Instalação

1. Clone o repositório:
```bash
git clone <repo-url>
cd github_dash
```

2. Instale as dependências:
```bash
pip install -r requirements.txt
```

3. Configure o token do GitHub:
```bash
cp .env.example .env
# Edite o .env com seu token do GitHub
```

4. Execute o dashboard:
```bash
streamlit run streamlit_app.py
```

## 🔧 Configuração

1. Obtenha um token do GitHub em: https://github.com/settings/tokens
2. Adicione o token no arquivo `.env`
3. Execute o aplicativo

## 📁 Estrutura do Projeto

```
github_dash/
├── streamlit_app.py       # UI principal
├── github_client.py       # Cliente GraphQL GitHub
├── models.py              # Modelos SQLAlchemy
├── utils.py               # Utilitários e cache
├── requirements.txt       # Dependências
├── .env.example          # Configuração exemplo
└── repos.db              # Banco SQLite (criado automaticamente)
```

## 🎯 Como Usar

1. Acesse o dashboard no navegador
2. Digite um username do GitHub
3. Explore as diferentes abas com análises
4. Compare usuários na sidebar
5. Exporte relatórios conforme necessário

## 🤝 Contribuição

Contribuições são bem-vindas! Abra uma issue ou envie um PR.

## 📄 Licença

MIT License
