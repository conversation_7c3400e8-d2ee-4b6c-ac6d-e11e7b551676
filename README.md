# 📊 GitHub Portfolio Analyzer

Um dashboard interativo e moderno para analisar portfólios do GitHub com Streamlit.

## ✨ Funcionalidades

- **Dashboard Multi-abas**: Visão Geral, Linguagens, Atividade e Detalhes
- **Análises <PERSON>n<PERSON>**: Heatmap de commits, word cloud, análise de linguagens
- **Comparação de Usuários**: Compare até 3 perfis GitHub lado a lado
- **Cache Inteligente**: Performance otimizada com cache de dados
- **Exportação**: Relatórios em CSV e PDF
- **Interface Moderna**: UI responsiva com tema claro/escuro

## 🚀 Instalação

1. Clone o repositório:
```bash
git clone <repo-url>
cd github_dash
```

2. Instale as dependências:
```bash
pip install -r requirements.txt
```

3. Configure o token do GitHub:
```bash
cp .env.example .env
# Edite o .env com seu token do GitHub
```

4. Execute o dashboard:
```bash
# Opção 1: Usando o script de execução
python run.py

# Opção 2: Diretamente com Streamlit
streamlit run streamlit_app.py
```

## 🔧 Configuração

1. Obtenha um token do GitHub em: https://github.com/settings/tokens
2. Adicione o token no arquivo `.env`
3. Execute o aplicativo

## 📁 Estrutura do Projeto

```
github_dash/
├── streamlit_app.py       # UI principal
├── github_client.py       # Cliente GraphQL GitHub
├── models.py              # Modelos SQLAlchemy
├── utils.py               # Utilitários e cache
├── requirements.txt       # Dependências
├── .env.example          # Configuração exemplo
└── repos.db              # Banco SQLite (criado automaticamente)
```

## 🎯 Como Usar

1. **Acesse o dashboard** no navegador (http://localhost:8501)
2. **Digite um username** do GitHub na sidebar
3. **Clique em "Analisar Perfil"** para buscar os dados
4. **Explore as abas**:
   - 🏠 **Visão Geral**: Métricas principais e word cloud
   - 💻 **Linguagens**: Análise de linguagens de programação
   - 📈 **Atividade**: Timeline e scatter plots
   - 📋 **Detalhes**: Tabela completa dos repositórios
   - 🔬 **Análises Avançadas**: Heatmaps e relatórios
5. **Compare usuários** na sidebar (até 3 usuários)
6. **Exporte relatórios** em CSV, JSON ou PDF

## 🔧 Funcionalidades Detalhadas

### 📊 Visualizações
- **Gráfico de Pizza**: Distribuição de linguagens
- **Treemap**: Linguagens por total de stars
- **Scatter Plot**: Relação stars vs forks
- **Timeline**: Atividade dos repositórios por mês
- **Heatmap**: Padrão de commits (simulado)
- **Word Cloud**: Palavras-chave das descrições

### 🔍 Filtros e Busca
- Filtro por período (30/90/365 dias)
- Incluir/excluir forks e repositórios arquivados
- Filtro por linguagem de programação
- Filtro por número mínimo de stars
- Ordenação personalizável

### 📈 Comparação de Usuários
- Compare até 3 perfis simultaneamente
- Gráficos comparativos de métricas
- Tabela lado a lado
- Análise de linguagens principais

### 📄 Exportação
- **CSV**: Dados dos repositórios
- **JSON**: Análise completa estruturada
- **PDF**: Relatório resumido (texto)

## 🤝 Contribuição

Contribuições são bem-vindas! Abra uma issue ou envie um PR.

## 📄 Licença

MIT License
