#!/usr/bin/env python3
"""
Script para executar o GitHub Portfolio Analyzer
"""

import subprocess
import sys
import os
from pathlib import Path

def check_requirements():
    """Verifica se as dependências estão instaladas"""
    try:
        import streamlit
        import pandas
        import plotly
        import sqlalchemy
        import requests
        print("✅ Todas as dependências estão instaladas")
        return True
    except ImportError as e:
        print(f"❌ Dependência faltando: {e}")
        print("Execute: pip install -r requirements.txt")
        return False

def check_env_file():
    """Verifica se o arquivo .env existe"""
    if not Path('.env').exists():
        print("⚠️  Arquivo .env não encontrado")
        print("Copie .env.example para .env e configure seu token do GitHub")
        return False
    
    # Verificar se o token está configurado
    from dotenv import load_dotenv
    load_dotenv()
    
    token = os.getenv('GITHUB_TOKEN')
    if not token or token == 'your_github_token_here':
        print("⚠️  Token do GitHub não configurado no arquivo .env")
        print("Configure GITHUB_TOKEN com seu token pessoal do GitHub")
        return False
    
    print("✅ Configuração do .env OK")
    return True

def run_app():
    """Executa o aplicativo Streamlit"""
    print("🚀 Iniciando GitHub Portfolio Analyzer...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao executar o aplicativo: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 Aplicativo encerrado pelo usuário")

def main():
    """Função principal"""
    print("📊 GitHub Portfolio Analyzer")
    print("=" * 40)
    
    # Verificar dependências
    if not check_requirements():
        sys.exit(1)
    
    # Verificar configuração
    if not check_env_file():
        sys.exit(1)
    
    # Executar aplicativo
    run_app()

if __name__ == "__main__":
    main()
