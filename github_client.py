"""
Cliente GitHub com integração GraphQL para buscar dados de forma eficiente
"""

import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import os
from dotenv import load_dotenv
from models import db_manager, User, Repository, Language, Commit, repo_languages
from sqlalchemy.orm import sessionmaker
from sqlalchemy import and_

load_dotenv()

class GitHubClient:
    """Cliente para interagir com a API do GitHub"""
    
    def __init__(self, token: str = None):
        self.token = token or os.getenv('GITHUB_TOKEN')
        if not self.token:
            raise ValueError("GitHub token is required. Set GITHUB_TOKEN environment variable.")
        
        self.headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json',
        }
        self.graphql_url = 'https://api.github.com/graphql'
        self.rest_url = 'https://api.github.com'
        
    def execute_graphql_query(self, query: str, variables: Dict = None) -> Dict:
        """Executa uma query GraphQL"""
        payload = {
            'query': query,
            'variables': variables or {}
        }
        
        response = requests.post(
            self.graphql_url,
            headers=self.headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"GraphQL query failed: {response.status_code} - {response.text}")
        
        data = response.json()
        if 'errors' in data:
            raise Exception(f"GraphQL errors: {data['errors']}")
        
        return data['data']
    
    def get_user_profile_query(self) -> str:
        """Query GraphQL para buscar perfil do usuário"""
        return """
        query GetUserProfile($username: String!) {
            user(login: $username) {
                id
                login
                name
                bio
                location
                company
                blog
                email
                avatarUrl
                followers {
                    totalCount
                }
                following {
                    totalCount
                }
                repositories(first: 100, orderBy: {field: UPDATED_AT, direction: DESC}) {
                    totalCount
                    nodes {
                        id
                        name
                        nameWithOwner
                        description
                        isPrivate
                        isFork
                        isArchived
                        isDisabled
                        url
                        sshUrl
                        diskUsage
                        stargazerCount
                        watcherCount
                        forkCount
                        issues(states: OPEN) {
                            totalCount
                        }
                        primaryLanguage {
                            name
                            color
                        }
                        languages(first: 10, orderBy: {field: SIZE, direction: DESC}) {
                            edges {
                                size
                                node {
                                    name
                                    color
                                }
                            }
                        }
                        createdAt
                        updatedAt
                        pushedAt
                    }
                }
                contributionsCollection {
                    totalCommitContributions
                    contributionCalendar {
                        totalContributions
                        weeks {
                            contributionDays {
                                contributionCount
                                date
                            }
                        }
                    }
                }
                createdAt
                updatedAt
            }
        }
        """
    
    def get_repository_commits_query(self) -> str:
        """Query GraphQL para buscar commits de um repositório"""
        return """
        query GetRepositoryCommits($owner: String!, $name: String!, $since: GitTimestamp) {
            repository(owner: $owner, name: $name) {
                defaultBranchRef {
                    target {
                        ... on Commit {
                            history(first: 100, since: $since) {
                                nodes {
                                    oid
                                    message
                                    author {
                                        name
                                        email
                                        date
                                    }
                                    committer {
                                        name
                                        email
                                        date
                                    }
                                    additions
                                    deletions
                                }
                            }
                        }
                    }
                }
            }
        }
        """
    
    def fetch_user_data(self, username: str) -> Tuple[User, List[Repository]]:
        """Busca dados completos do usuário via GraphQL"""
        query = self.get_user_profile_query()
        variables = {'username': username}
        
        data = self.execute_graphql_query(query, variables)
        user_data = data['user']
        
        if not user_data:
            raise ValueError(f"User '{username}' not found")
        
        # Criar objeto User
        user = User(
            username=user_data['login'],
            name=user_data.get('name'),
            bio=user_data.get('bio'),
            location=user_data.get('location'),
            company=user_data.get('company'),
            blog=user_data.get('blog'),
            email=user_data.get('email'),
            avatar_url=user_data.get('avatarUrl'),
            followers=user_data['followers']['totalCount'],
            following=user_data['following']['totalCount'],
            public_repos=user_data['repositories']['totalCount'],
            created_at=datetime.fromisoformat(user_data['createdAt'].replace('Z', '+00:00')),
            updated_at=datetime.fromisoformat(user_data['updatedAt'].replace('Z', '+00:00')),
            last_fetched=datetime.utcnow()
        )
        
        # Processar repositórios
        repositories = []
        for repo_data in user_data['repositories']['nodes']:
            repo = Repository(
                github_id=int(repo_data['id']),
                name=repo_data['name'],
                full_name=repo_data['nameWithOwner'],
                description=repo_data.get('description'),
                private=repo_data['isPrivate'],
                fork=repo_data['isFork'],
                archived=repo_data['isArchived'],
                disabled=repo_data['isDisabled'],
                html_url=repo_data['url'],
                ssh_url=repo_data['sshUrl'],
                size=repo_data.get('diskUsage', 0),
                stargazers_count=repo_data['stargazerCount'],
                watchers_count=repo_data['watcherCount'],
                forks_count=repo_data['forkCount'],
                open_issues_count=repo_data['issues']['totalCount'],
                primary_language=repo_data['primaryLanguage']['name'] if repo_data['primaryLanguage'] else None,
                created_at=datetime.fromisoformat(repo_data['createdAt'].replace('Z', '+00:00')),
                updated_at=datetime.fromisoformat(repo_data['updatedAt'].replace('Z', '+00:00')),
                pushed_at=datetime.fromisoformat(repo_data['pushedAt'].replace('Z', '+00:00')) if repo_data['pushedAt'] else None,
                last_fetched=datetime.utcnow()
            )
            repositories.append(repo)
        
        return user, repositories
    
    def fetch_repository_commits(self, owner: str, repo_name: str, since_days: int = 365) -> List[Commit]:
        """Busca commits de um repositório"""
        since_date = datetime.utcnow() - timedelta(days=since_days)
        since_iso = since_date.isoformat() + 'Z'
        
        query = self.get_repository_commits_query()
        variables = {
            'owner': owner,
            'name': repo_name,
            'since': since_iso
        }
        
        try:
            data = self.execute_graphql_query(query, variables)
            repo_data = data['repository']
            
            if not repo_data or not repo_data['defaultBranchRef']:
                return []
            
            commits = []
            commit_nodes = repo_data['defaultBranchRef']['target']['history']['nodes']
            
            for commit_data in commit_nodes:
                commit = Commit(
                    sha=commit_data['oid'],
                    message=commit_data['message'],
                    author_name=commit_data['author']['name'],
                    author_email=commit_data['author']['email'],
                    committer_name=commit_data['committer']['name'],
                    committer_email=commit_data['committer']['email'],
                    date=datetime.fromisoformat(commit_data['author']['date'].replace('Z', '+00:00')),
                    additions=commit_data.get('additions', 0),
                    deletions=commit_data.get('deletions', 0)
                )
                commits.append(commit)
            
            return commits
        except Exception as e:
            print(f"Error fetching commits for {owner}/{repo_name}: {e}")
            return []
    
    def save_user_data(self, user: User, repositories: List[Repository]) -> None:
        """Salva dados do usuário no banco de dados"""
        session = db_manager.get_session()
        
        try:
            # Verificar se usuário já existe
            existing_user = session.query(User).filter_by(username=user.username).first()
            if existing_user:
                # Atualizar dados existentes
                for attr in ['name', 'bio', 'location', 'company', 'blog', 'email', 
                           'avatar_url', 'followers', 'following', 'public_repos']:
                    setattr(existing_user, attr, getattr(user, attr))
                existing_user.last_fetched = datetime.utcnow()
                user = existing_user
            else:
                session.add(user)
                session.flush()  # Para obter o ID
            
            # Processar repositórios
            for repo in repositories:
                repo.owner_id = user.id
                
                existing_repo = session.query(Repository).filter_by(github_id=repo.github_id).first()
                if existing_repo:
                    # Atualizar repositório existente
                    for attr in ['name', 'full_name', 'description', 'private', 'fork', 
                               'archived', 'disabled', 'size', 'stargazers_count', 
                               'watchers_count', 'forks_count', 'open_issues_count',
                               'primary_language', 'updated_at', 'pushed_at']:
                        setattr(existing_repo, attr, getattr(repo, attr))
                    existing_repo.last_fetched = datetime.utcnow()
                else:
                    session.add(repo)
            
            session.commit()
            
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

# Instância global do cliente
github_client = GitHubClient()
