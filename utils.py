"""
U<PERSON><PERSON><PERSON><PERSON><PERSON>, cache e análises avançadas para o GitHub Portfolio Analyzer
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import altair as alt
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import networkx as nx
from wordcloud import WordCloud
import matplotlib.pyplot as plt
import seaborn as sns
from models import db_manager, User, Repository, Language, Commit
from sqlalchemy import func, desc, and_
import io
import base64
from PIL import Image

class DataProcessor:
    """Processador de dados com cache e análises"""
    
    @staticmethod
    @st.cache_data(ttl=3600)
    def get_user_summary(username: str) -> Dict:
        """Busca resumo do usuário com cache"""
        session = db_manager.get_session()
        
        try:
            user = session.query(User).filter_by(username=username).first()
            if not user:
                return None
            
            # Estatísticas básicas
            total_repos = session.query(Repository).filter_by(owner_id=user.id).count()
            total_stars = session.query(func.sum(Repository.stargazers_count)).filter_by(owner_id=user.id).scalar() or 0
            total_forks = session.query(func.sum(Repository.forks_count)).filter_by(owner_id=user.id).scalar() or 0
            
            # Repositório mais popular
            top_repo = session.query(Repository).filter_by(owner_id=user.id).order_by(desc(Repository.stargazers_count)).first()
            
            # Último commit
            last_repo = session.query(Repository).filter_by(owner_id=user.id).order_by(desc(Repository.pushed_at)).first()
            
            return {
                'user': {
                    'username': user.username,
                    'name': user.name,
                    'bio': user.bio,
                    'location': user.location,
                    'company': user.company,
                    'avatar_url': user.avatar_url,
                    'followers': user.followers,
                    'following': user.following,
                    'created_at': user.created_at
                },
                'stats': {
                    'total_repos': total_repos,
                    'total_stars': total_stars,
                    'total_forks': total_forks,
                    'top_repo': {
                        'name': top_repo.name if top_repo else None,
                        'stars': top_repo.stargazers_count if top_repo else 0,
                        'url': top_repo.html_url if top_repo else None
                    },
                    'last_activity': last_repo.pushed_at if last_repo else None
                }
            }
        finally:
            session.close()
    
    @staticmethod
    @st.cache_data(ttl=3600)
    def get_language_stats(username: str) -> pd.DataFrame:
        """Estatísticas de linguagens com cache"""
        session = db_manager.get_session()
        
        try:
            user = session.query(User).filter_by(username=username).first()
            if not user:
                return pd.DataFrame()
            
            # Query para linguagens por repositório
            repos = session.query(Repository).filter_by(owner_id=user.id).all()
            
            language_data = {}
            for repo in repos:
                if repo.primary_language:
                    if repo.primary_language not in language_data:
                        language_data[repo.primary_language] = {
                            'repos': 0,
                            'stars': 0,
                            'size': 0
                        }
                    language_data[repo.primary_language]['repos'] += 1
                    language_data[repo.primary_language]['stars'] += repo.stargazers_count
                    language_data[repo.primary_language]['size'] += repo.size
            
            if not language_data:
                return pd.DataFrame()
            
            df = pd.DataFrame.from_dict(language_data, orient='index')
            df.index.name = 'language'
            df = df.reset_index()
            df = df.sort_values('repos', ascending=False)
            
            return df
        finally:
            session.close()
    
    @staticmethod
    @st.cache_data(ttl=3600)
    def get_repository_data(username: str) -> pd.DataFrame:
        """Dados dos repositórios com cache"""
        session = db_manager.get_session()
        
        try:
            user = session.query(User).filter_by(username=username).first()
            if not user:
                return pd.DataFrame()
            
            repos = session.query(Repository).filter_by(owner_id=user.id).all()
            
            data = []
            for repo in repos:
                data.append({
                    'name': repo.name,
                    'description': repo.description or '',
                    'language': repo.primary_language or 'Unknown',
                    'stars': repo.stargazers_count,
                    'forks': repo.forks_count,
                    'size': repo.size,
                    'issues': repo.open_issues_count,
                    'created_at': repo.created_at,
                    'updated_at': repo.updated_at,
                    'pushed_at': repo.pushed_at,
                    'url': repo.html_url,
                    'is_fork': repo.fork,
                    'is_archived': repo.archived
                })
            
            return pd.DataFrame(data)
        finally:
            session.close()

class Visualizations:
    """Classe para gerar visualizações"""
    
    @staticmethod
    def create_language_pie_chart(df: pd.DataFrame) -> go.Figure:
        """Gráfico de pizza das linguagens"""
        if df.empty:
            return go.Figure()
        
        fig = px.pie(
            df.head(10), 
            values='repos', 
            names='language',
            title='Distribuição de Linguagens por Repositórios',
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        
        fig.update_traces(
            textposition='inside', 
            textinfo='percent+label',
            hovertemplate='<b>%{label}</b><br>Repositórios: %{value}<br>Percentual: %{percent}<extra></extra>'
        )
        
        return fig
    
    @staticmethod
    def create_language_treemap(df: pd.DataFrame) -> go.Figure:
        """Treemap das linguagens por stars"""
        if df.empty:
            return go.Figure()
        
        fig = px.treemap(
            df.head(15),
            path=['language'],
            values='stars',
            title='Linguagens por Total de Stars',
            color='stars',
            color_continuous_scale='Viridis'
        )
        
        return fig
    
    @staticmethod
    def create_repo_scatter(df: pd.DataFrame) -> go.Figure:
        """Scatter plot repositórios: stars vs forks"""
        if df.empty:
            return go.Figure()
        
        fig = px.scatter(
            df,
            x='stars',
            y='forks',
            size='size',
            color='language',
            hover_name='name',
            hover_data=['description', 'issues'],
            title='Repositórios: Stars vs Forks',
            labels={'stars': 'Stars', 'forks': 'Forks', 'size': 'Tamanho (KB)'}
        )
        
        fig.update_layout(
            xaxis_type="log" if df['stars'].max() > 100 else "linear",
            yaxis_type="log" if df['forks'].max() > 100 else "linear"
        )
        
        return fig
    
    @staticmethod
    def create_activity_timeline(df: pd.DataFrame) -> go.Figure:
        """Timeline de atividade dos repositórios"""
        if df.empty:
            return go.Figure()
        
        # Preparar dados
        df_timeline = df.copy()
        df_timeline['year_month'] = pd.to_datetime(df_timeline['pushed_at']).dt.to_period('M')
        
        activity = df_timeline.groupby(['year_month', 'language']).size().reset_index(name='count')
        activity['year_month'] = activity['year_month'].astype(str)
        
        fig = px.bar(
            activity,
            x='year_month',
            y='count',
            color='language',
            title='Atividade dos Repositórios por Mês',
            labels={'count': 'Número de Repositórios Ativos', 'year_month': 'Mês/Ano'}
        )
        
        fig.update_layout(xaxis_tickangle=-45)
        
        return fig
    
    @staticmethod
    def create_metrics_cards(stats: Dict) -> None:
        """Cria cards de métricas"""
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="📁 Total de Repositórios",
                value=stats['total_repos']
            )
        
        with col2:
            st.metric(
                label="⭐ Total de Stars",
                value=stats['total_stars']
            )
        
        with col3:
            st.metric(
                label="🍴 Total de Forks",
                value=stats['total_forks']
            )
        
        with col4:
            if stats['top_repo']['name']:
                st.metric(
                    label="🏆 Repo Mais Popular",
                    value=stats['top_repo']['name'],
                    delta=f"{stats['top_repo']['stars']} stars"
                )
            else:
                st.metric(
                    label="🏆 Repo Mais Popular",
                    value="N/A"
                )

class WordCloudGenerator:
    """Gerador de word clouds"""
    
    @staticmethod
    @st.cache_data(ttl=3600)
    def generate_description_wordcloud(username: str) -> str:
        """Gera word cloud das descrições dos repositórios"""
        session = db_manager.get_session()
        
        try:
            user = session.query(User).filter_by(username=username).first()
            if not user:
                return None
            
            repos = session.query(Repository).filter_by(owner_id=user.id).all()
            
            # Coletar todas as descrições
            descriptions = []
            for repo in repos:
                if repo.description:
                    descriptions.append(repo.description)
            
            if not descriptions:
                return None
            
            text = ' '.join(descriptions)
            
            # Gerar word cloud
            wordcloud = WordCloud(
                width=800,
                height=400,
                background_color='white',
                colormap='viridis',
                max_words=100,
                relative_scaling=0.5,
                random_state=42
            ).generate(text)
            
            # Converter para base64
            img = wordcloud.to_image()
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            return img_str
        finally:
            session.close()

class ExportUtils:
    """Utilitários para exportação"""
    
    @staticmethod
    def export_to_csv(df: pd.DataFrame, filename: str) -> bytes:
        """Exporta DataFrame para CSV"""
        return df.to_csv(index=False).encode('utf-8')
    
    @staticmethod
    def create_download_link(data: bytes, filename: str, text: str) -> str:
        """Cria link de download"""
        b64 = base64.b64encode(data).decode()
        return f'<a href="data:file/csv;base64,{b64}" download="{filename}">{text}</a>'

# Instâncias globais
data_processor = DataProcessor()
visualizations = Visualizations()
wordcloud_generator = WordCloudGenerator()
export_utils = ExportUtils()
