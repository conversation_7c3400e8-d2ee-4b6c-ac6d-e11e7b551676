"""
GitHub Portfolio Analyzer - Dashboard Principal
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv

# Configuração da página
st.set_page_config(
    page_title="📊 GitHub Portfolio Analyzer",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Carregar variáveis de ambiente
load_dotenv()

# Imports locais
from models import db_manager
from github_client import github_client
from utils import (data_processor, visualizations, wordcloud_generator,
                  export_utils, user_comparison, advanced_analytics)

# CSS customizado
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        margin-bottom: 2rem;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .repo-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        background: #f9f9f9;
    }
    
    .sidebar-info {
        background: #f0f2f6;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def initialize_database():
    """Inicializa o banco de dados"""
    try:
        db_manager.create_tables()
        return True
    except Exception as e:
        st.error(f"Erro ao inicializar banco de dados: {e}")
        return False

def main():
    """Função principal do dashboard"""
    
    # Inicializar banco
    if not initialize_database():
        st.stop()
    
    # Header principal
    st.markdown('<h1 class="main-header">📊 GitHub Portfolio Analyzer</h1>', unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.markdown("## ⚙️ Configurações")
        
        # Input do usuário
        username = st.text_input(
            "👤 Username do GitHub",
            placeholder="Digite o username...",
            help="Digite o nome de usuário do GitHub para análise"
        )
        
        # Botão para buscar dados
        if st.button("🔍 Analisar Perfil", type="primary", use_container_width=True):
            if username:
                with st.spinner("Buscando dados do GitHub..."):
                    try:
                        user, repositories = github_client.fetch_user_data(username)
                        github_client.save_user_data(user, repositories)
                        st.success(f"✅ Dados de {username} atualizados!")
                        st.rerun()
                    except Exception as e:
                        st.error(f"❌ Erro ao buscar dados: {e}")
            else:
                st.warning("⚠️ Digite um username válido")
        
        st.markdown("---")
        
        # Filtros
        st.markdown("## 🔧 Filtros")
        
        period_filter = st.selectbox(
            "📅 Período de Análise",
            ["Últimos 30 dias", "Últimos 90 dias", "Último ano", "Todos os dados"],
            index=3
        )
        
        show_forks = st.checkbox("🍴 Incluir Forks", value=False)
        show_archived = st.checkbox("📦 Incluir Arquivados", value=False)
        
        st.markdown("---")
        
        # Comparação de usuários
        st.markdown("## 👥 Comparar Usuários")
        compare_users = st.text_area(
            "Usuários para comparar",
            placeholder="user1, user2, user3",
            help="Digite até 3 usuários separados por vírgula"
        )

        if st.button("📊 Comparar", use_container_width=True):
            if compare_users:
                usernames_list = [u.strip() for u in compare_users.split(',') if u.strip()]
                if len(usernames_list) > 3:
                    st.warning("⚠️ Máximo de 3 usuários para comparação")
                else:
                    st.session_state['compare_users'] = usernames_list
                    st.success(f"✅ Comparando: {', '.join(usernames_list)}")
                    st.rerun()
    
    # Conteúdo principal
    if not username:
        st.markdown("""
        ## 👋 Bem-vindo ao GitHub Portfolio Analyzer!
        
        Este dashboard permite analisar perfis do GitHub de forma interativa e visual.
        
        ### 🚀 Como usar:
        1. Digite um username do GitHub na sidebar
        2. Clique em "Analisar Perfil" para buscar os dados
        3. Explore as diferentes abas com análises detalhadas
        
        ### ✨ Funcionalidades:
        - 📈 Análise completa de repositórios
        - 🎨 Visualizações interativas
        - 📊 Estatísticas de linguagens
        - 🔥 Mapa de atividade
        - 📋 Exportação de relatórios
        """)
        return
    
    # Buscar dados do usuário
    user_data = data_processor.get_user_summary(username)
    
    if not user_data:
        st.warning(f"⚠️ Usuário '{username}' não encontrado. Clique em 'Analisar Perfil' para buscar os dados.")
        return
    
    # Verificar se há comparação ativa
    if 'compare_users' in st.session_state and st.session_state['compare_users']:
        show_comparison_tab(st.session_state['compare_users'])

        if st.button("🔄 Voltar ao Perfil Individual"):
            del st.session_state['compare_users']
            st.rerun()
        return

    # Tabs principais
    tab_overview, tab_languages, tab_activity, tab_details, tab_advanced = st.tabs([
        "🏠 Visão Geral", "💻 Linguagens", "📈 Atividade", "📋 Detalhes", "🔬 Análises Avançadas"
    ])

    with tab_overview:
        show_overview_tab(user_data, username)

    with tab_languages:
        show_languages_tab(username)

    with tab_activity:
        show_activity_tab(username)

    with tab_details:
        show_details_tab(username, show_forks, show_archived)

    with tab_advanced:
        show_advanced_tab(username)

def show_overview_tab(user_data, username):
    """Aba de visão geral"""
    user_info = user_data['user']
    stats = user_data['stats']
    
    # Perfil do usuário
    col1, col2 = st.columns([1, 3])
    
    with col1:
        if user_info['avatar_url']:
            st.image(user_info['avatar_url'], width=150)
    
    with col2:
        st.markdown(f"# {user_info['name'] or user_info['username']}")
        st.markdown(f"**@{user_info['username']}**")
        
        if user_info['bio']:
            st.markdown(f"*{user_info['bio']}*")
        
        info_cols = st.columns(3)
        with info_cols[0]:
            if user_info['location']:
                st.markdown(f"📍 {user_info['location']}")
        with info_cols[1]:
            if user_info['company']:
                st.markdown(f"🏢 {user_info['company']}")
        with info_cols[2]:
            st.markdown(f"👥 {user_info['followers']} seguidores")
    
    st.markdown("---")
    
    # Cards de métricas
    visualizations.create_metrics_cards(stats)
    
    st.markdown("---")
    
    # Word Cloud
    st.markdown("## ☁️ Word Cloud das Descrições")
    wordcloud_img = wordcloud_generator.generate_description_wordcloud(username)
    
    if wordcloud_img:
        st.markdown(
            f'<img src="data:image/png;base64,{wordcloud_img}" style="width:100%">',
            unsafe_allow_html=True
        )
    else:
        st.info("📝 Não há descrições suficientes para gerar o word cloud")

def show_languages_tab(username):
    """Aba de linguagens"""
    st.markdown("## 💻 Análise de Linguagens")
    
    # Buscar dados de linguagens
    lang_df = data_processor.get_language_stats(username)
    
    if lang_df.empty:
        st.info("📊 Não há dados de linguagens disponíveis")
        return
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Gráfico de pizza
        pie_fig = visualizations.create_language_pie_chart(lang_df)
        st.plotly_chart(pie_fig, use_container_width=True)
    
    with col2:
        # Treemap
        treemap_fig = visualizations.create_language_treemap(lang_df)
        st.plotly_chart(treemap_fig, use_container_width=True)
    
    # Tabela de linguagens
    st.markdown("### 📊 Estatísticas Detalhadas")
    st.dataframe(
        lang_df,
        use_container_width=True,
        column_config={
            "language": "Linguagem",
            "repos": st.column_config.NumberColumn("Repositórios", format="%d"),
            "stars": st.column_config.NumberColumn("Total Stars", format="%d"),
            "size": st.column_config.NumberColumn("Tamanho Total (KB)", format="%d")
        }
    )

def show_activity_tab(username):
    """Aba de atividade"""
    st.markdown("## 📈 Análise de Atividade")
    
    # Buscar dados dos repositórios
    repo_df = data_processor.get_repository_data(username)
    
    if repo_df.empty:
        st.info("📊 Não há dados de atividade disponíveis")
        return
    
    # Timeline de atividade
    timeline_fig = visualizations.create_activity_timeline(repo_df)
    st.plotly_chart(timeline_fig, use_container_width=True)
    
    # Scatter plot
    scatter_fig = visualizations.create_repo_scatter(repo_df)
    st.plotly_chart(scatter_fig, use_container_width=True)

def show_details_tab(username, show_forks, show_archived):
    """Aba de detalhes"""
    st.markdown("## 📋 Detalhes dos Repositórios")
    
    # Buscar dados dos repositórios
    repo_df = data_processor.get_repository_data(username)
    
    if repo_df.empty:
        st.info("📊 Não há repositórios disponíveis")
        return
    
    # Aplicar filtros
    filtered_df = repo_df.copy()
    
    if not show_forks:
        filtered_df = filtered_df[~filtered_df['is_fork']]
    
    if not show_archived:
        filtered_df = filtered_df[~filtered_df['is_archived']]
    
    # Filtros adicionais
    col1, col2, col3 = st.columns(3)
    
    with col1:
        languages = ['Todas'] + sorted(filtered_df['language'].unique().tolist())
        selected_lang = st.selectbox("🔍 Filtrar por Linguagem", languages)
    
    with col2:
        min_stars = st.number_input("⭐ Mínimo de Stars", min_value=0, value=0)
    
    with col3:
        sort_by = st.selectbox("📊 Ordenar por", 
                              ['stars', 'forks', 'updated_at', 'created_at', 'size'])
    
    # Aplicar filtros
    if selected_lang != 'Todas':
        filtered_df = filtered_df[filtered_df['language'] == selected_lang]
    
    filtered_df = filtered_df[filtered_df['stars'] >= min_stars]
    filtered_df = filtered_df.sort_values(sort_by, ascending=False)
    
    # Exibir resultados
    st.markdown(f"**{len(filtered_df)} repositórios encontrados**")
    
    # Botão de exportação
    if st.button("📥 Exportar CSV"):
        csv_data = export_utils.export_to_csv(filtered_df, f"{username}_repositories.csv")
        st.download_button(
            label="⬇️ Download CSV",
            data=csv_data,
            file_name=f"{username}_repositories.csv",
            mime="text/csv"
        )
    
    # Tabela interativa
    st.dataframe(
        filtered_df,
        use_container_width=True,
        column_config={
            "name": "Nome",
            "description": "Descrição",
            "language": "Linguagem",
            "stars": st.column_config.NumberColumn("⭐ Stars", format="%d"),
            "forks": st.column_config.NumberColumn("🍴 Forks", format="%d"),
            "size": st.column_config.NumberColumn("📦 Tamanho (KB)", format="%d"),
            "issues": st.column_config.NumberColumn("🐛 Issues", format="%d"),
            "url": st.column_config.LinkColumn("🔗 Link"),
            "updated_at": st.column_config.DatetimeColumn("📅 Atualizado"),
            "created_at": st.column_config.DatetimeColumn("📅 Criado")
        },
        hide_index=True
    )

def show_comparison_tab(usernames_list):
    """Aba de comparação entre usuários"""
    st.markdown("## 👥 Comparação entre Usuários")

    # Buscar dados de comparação
    comparison_data = user_comparison.compare_users(usernames_list)

    if not comparison_data:
        st.error("❌ Não foi possível encontrar dados para os usuários especificados")
        return

    # Exibir perfis lado a lado
    cols = st.columns(len(comparison_data))

    for i, (username, data) in enumerate(comparison_data.items()):
        with cols[i]:
            st.markdown(f"### {data['name']}")
            if data['avatar_url']:
                st.image(data['avatar_url'], width=100)

            st.metric("👥 Seguidores", data['followers'])
            st.metric("📁 Repositórios", data['total_repos'])
            st.metric("⭐ Stars", data['total_stars'])
            st.metric("🍴 Forks", data['total_forks'])
            st.metric("💻 Linguagem Principal", data['top_language'])

    # Gráfico de comparação
    comparison_fig = user_comparison.create_comparison_chart(comparison_data)
    st.plotly_chart(comparison_fig, use_container_width=True)

    # Tabela de comparação
    st.markdown("### 📊 Tabela Comparativa")
    comparison_df = pd.DataFrame.from_dict(comparison_data, orient='index')
    st.dataframe(comparison_df, use_container_width=True)

def show_advanced_tab(username):
    """Aba de análises avançadas"""
    st.markdown("## 🔬 Análises Avançadas")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 🔥 Heatmap de Commits")
        heatmap_data = advanced_analytics.get_commit_heatmap_data(username)
        heatmap_fig = advanced_analytics.create_commit_heatmap(heatmap_data)
        st.plotly_chart(heatmap_fig, use_container_width=True)

    with col2:
        st.markdown("### 🕸️ Rede de Colaboração")
        network_data = advanced_analytics.create_network_graph(username)

        # Exibir informações da rede (placeholder)
        st.info("🚧 Visualização de rede em desenvolvimento")
        st.json(network_data)

    # Relatórios
    st.markdown("---")
    st.markdown("### 📄 Relatórios")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("📊 Gerar Relatório PDF", use_container_width=True):
            user_data = data_processor.get_user_summary(username)
            lang_df = data_processor.get_language_stats(username)
            repo_df = data_processor.get_repository_data(username)

            pdf_data = export_utils.generate_pdf_report(username, user_data, lang_df, repo_df)

            st.download_button(
                label="⬇️ Download Relatório",
                data=pdf_data,
                file_name=f"{username}_report.txt",
                mime="text/plain"
            )

    with col2:
        if st.button("📈 Análise Completa CSV", use_container_width=True):
            repo_df = data_processor.get_repository_data(username)
            lang_df = data_processor.get_language_stats(username)

            # Combinar dados
            combined_data = {
                'repositories': repo_df.to_dict('records'),
                'languages': lang_df.to_dict('records'),
                'generated_at': datetime.now().isoformat()
            }

            import json
            json_data = json.dumps(combined_data, indent=2, default=str)

            st.download_button(
                label="⬇️ Download Análise JSON",
                data=json_data.encode('utf-8'),
                file_name=f"{username}_analysis.json",
                mime="application/json"
            )

if __name__ == "__main__":
    main()
